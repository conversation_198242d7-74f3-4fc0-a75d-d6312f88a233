// import { useEffect } from 'react'
import electronBridge from '../ElectronBridge'
// import axios from 'axios'
import './App.css'

function App() {
  return (
    <div className="popup-container">
      <button
        className="btnStyle"
        onClick={() => {
          console.log(electronBridge.curCookies)
        }}>
        查看cookie
      </button>
      <button
        onClick={async () => {
          const res = await electronBridge.invoke('ping')
          console.log(res)
        }}
        className="btnStyle">
        ping
      </button>
      <button
        onClick={async () => {
          const res = await electronBridge.invoke('reload')
          console.log(res)
        }}
        className="btnStyle">
        重新加载
      </button>
      <button
        onClick={async () => {
          // document
          //   .querySelector('#douyin-creator-master-menu-nav-content')
          //   ?.click()
          // const dom = window.document.querySelector(
          //   '#douyin-creator-master-menu-nav-work_manage'
          // )
          // dom?.click()
        }}
        className="btnStyle">
        触发dom
      </button>
    </div>
  )
}

export default App
