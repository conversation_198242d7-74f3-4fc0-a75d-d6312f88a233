chrome.runtime.onMessage.addListener((msg, sender, sendResponse) => {
  if (msg.type === 'FETCH_CAPTURE' || msg.type === 'XHR_CAPTURE') {
    console.log('收到拦截数据:', msg.url, msg.data)

    // 存储数据到 chrome.storage.local
    chrome.storage.local.get('captured', (res) => {
      const captured = res.captured || []
      captured.push({ url: msg.url, data: msg.data, time: Date.now() })

      chrome.storage.local.set({ captured })
    })
  }
})
