// import { createRoot } from 'react-dom/client'
// import App from './views/App.tsx'

// console.log('[CRXJS] Hello world from content script!')
// const container = document.createElement('div')
// container.id = 'crxjs-app'
// document.body.appendChild(container)
// createRoot(container).render(<App />)

// 劫持 fetch
;(function () {
  const rawFetch = window.fetch
  window.fetch = async (...args) => {
    const res = await rawFetch(...args)

    // 克隆一份响应，避免被页面消耗掉
    res
      .clone()
      .text()
      .then((text) => {
        try {
          const data = JSON.parse(text)
          chrome.runtime.sendMessage({
            type: 'FETCH_CAPTURE',
            url: args[0],
            data,
          })
        } catch (e) {
          // 不是 JSON 就不处理
        }
      })

    return res
  }

  // 劫持 XHR
  const rawOpen = XMLHttpRequest.prototype.open
  XMLHttpRequest.prototype.open = function (...args) {
    this.addEventListener('load', function () {
      try {
        const data = JSON.parse(this.responseText)
        chrome.runtime.sendMessage({
          type: 'XHR_CAPTURE',
          url: args[1],
          data,
        })
      } catch (e) {
        // 不是 JSON 就忽略
      }
    })
    return rawOpen.apply(this, args)
  }
})()
