// 插件与Electron通信的工具类
type MessageType = {
  from: string
  event: string
  success?: boolean
  error?: string
  data: Record<string, any>
  eventId: string
}

class ElectronBridge {
  curCookies = []
  winId: number = 0
  pendingCalls: any

  constructor() {
    this.setupMessageListener()
    this.pendingCalls = new Map()
  }

  // 设置消息监听器
  setupMessageListener() {
    window.addEventListener('message', (messageData) => {
      const { from } = messageData.data
      if (from === 'electron-preload') {
        this.handlePreloadResponse(messageData.data)
      } else if (from === 'electron-main') {
        this.handleMainProcessMessage(messageData.data)
      }
    })
  }

  // 处理preload的响应
  handlePreloadResponse(message: MessageType) {
    console.log('插件收到preload消息:', message)
    const { eventId, success, data, error } = message
    const pendingCall = this.pendingCalls.get(eventId)

    if (pendingCall) {
      this.pendingCalls.delete(eventId)
      if (success) {
        pendingCall.resolve(data)
      } else {
        pendingCall.reject(new Error(error))
      }
    }
  }

  // 处理主进程主动推送的消息
  handleMainProcessMessage(message: MessageType) {
    const { event, data } = message
    console.log('插件收到主进程推送的消息:', message)
    switch (event) {
      case 'initial-data':
        this.winId = data.winId
        this.curCookies = data.curCookies
          .map((item: any) => `${item.name}:${item.value}`)
          .join(';')
        break

      default:
        break
    }
  }

  // 主动调用IPC handler方法（异步）
  async invoke(event: string, args?: Record<string, any>) {
    return new Promise((resolve, reject) => {
      const eventId = this.generateId()
      this.pendingCalls.set(eventId, { resolve, reject })
      // 发送消息给preload
      window.postMessage(
        {
          from: 'extension',
          event,
          data: { ...args },
          eventId: eventId,
        },
        '*'
      )

      // 设置超时
      setTimeout(() => {
        if (this.pendingCalls.has(eventId)) {
          this.pendingCalls.delete(eventId)
          reject(new Error(`调用 ${event} 超时`))
        }
      }, 5000)
    })
  }

  // 生成调用invoke事件ID
  generateId() {
    return `ext_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
}

export default new ElectronBridge()
