<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>Captured Data</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 10px;
    }

    .item {
      padding: 6px;
      border-bottom: 1px solid #ccc;
    }

    .url {
      font-weight: bold;
      font-size: 12px;
      color: #333;
    }

    .time {
      font-size: 10px;
      color: #999;
    }

    pre {
      background: #f6f6f6;
      padding: 6px;
      border-radius: 4px;
      white-space: pre-wrap;
      word-break: break-all;
    }
  </style>
</head>

<body>
  <h2>捕获数据</h2>
  <div id="list">加载中...</div>
  <script>
    function formatTime (ts) {
      const d = new Date(ts)
      return d.toLocaleString()
    }

    chrome.storage.local.get("captured", (res) => {
      const list = document.getElementById("list")
      list.innerHTML = ""

      const captured = res.captured || []
      if (captured.length === 0) {
        list.textContent = "暂无数据"
        return
      }

      captured.slice().reverse().forEach(item => {
        const div = document.createElement("div")
        div.className = "item"

        div.innerHTML = `
      <div class="url">${item.url}</div>
      <div class="time">${formatTime(item.time)}</div>
      <pre>${JSON.stringify(item.data, null, 2)}</pre>
    `

        list.appendChild(div)
      })
    });

  </script>
</body>

</html>