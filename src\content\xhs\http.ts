import axios, { InternalAxiosRequestConfig } from 'axios'
import ElectronBridge from '../ElectronBridge'

const baseUrl = 'https://creator.xiaohongshu.com'

const request = axios.create({
  baseURL: baseUrl,
  timeout: 10000,
  withCredentials: true,
})

// 请求拦截器
request.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  // config.headers.Cookie = ElectronBridge.curCookies
  return config
})

export default request
